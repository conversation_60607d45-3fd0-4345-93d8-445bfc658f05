import {
  RazorpaySubmitOrderRequestVariables,
  RazorpaySubmitOrderResponse,
  RazorypayCreateOrderVariables,
  useAvailablePaymentMethods,
  useClearErrors,
  useGetSelectedPaymentMethod,
  useGetShoppingCart,
  useGlobalLoading,
  useIsMobileApp,
  useLinkInteractionEvent,
  usePageRouting,
  usePaymentAutoSaveData,
  useRazorpayCreateOrder,
  useRazorPayErrorEvent,
  useRazorpaySubmitOrder,
} from 'Feature/NewCheckoutFow';
import { contactEmailAtom, geoIpDataAtom, resetAfterSubmitPaymentAtom } from 'Feature/NewCheckoutFow/atoms';
import { RazorpayPaymentMethods } from 'Feature/Payment.Razorpay/client/BackendConstants.Generated';
import { CONFIRMATION_ROUTE } from 'Foundation/Commerce/client/Checkout/constants';
import { PaymentMethodCodes } from 'Foundation/Payment/client/BackendConstants.Generated';
import { useAtomValue, useSetAtom } from 'jotai';
import React, { useState } from 'react';
import Helmet from 'react-helmet';
import { RazorpayAuthorizationFailureResponse, RazorpayOptions, RazorpayPlaceOrderButtonProps } from './models';
import { getCurrentPageOrigin } from 'Feature/NewCheckoutFow/utils';
import PageLoader from '@pmi/dsm-react/dist/components/overlays/PageLoader';
import Button from '@pmi/dsm-react/dist/components/button/Button';

const paymentMethodCodeMapping = {
  [PaymentMethodCodes.RazorpayCreditCard]: RazorpayPaymentMethods.CreditCard,
  [PaymentMethodCodes.RazorpayNetbanking]: RazorpayPaymentMethods.NetBanking,
  [PaymentMethodCodes.RazorpayUpi]: RazorpayPaymentMethods.Upi,
};

const getAvailablePaymentRazorpayMethods = (availablePaymentMethodCodes: string[]) => {
  const razorpayPaymentMethods = Object.keys(paymentMethodCodeMapping);
  return availablePaymentMethodCodes.filter((code) => razorpayPaymentMethods.includes(code));
};

const getPaymentMethodSequence = (availablePaymentMethodCodes: string[], paymentMethodCode: string): string[] => {
  const sequence = availablePaymentMethodCodes.map((code) => paymentMethodCodeMapping[code]);
  const code = paymentMethodCodeMapping[paymentMethodCode];
  sequence.sort((a, _) => {
    return a === code ? -1 : 0;
  });

  return sequence;
};

const Component: React.FC<RazorpayPlaceOrderButtonProps> = ({ fields }) => {
  const adobeRegion = 'spx-content-col-right:order-summary';
  const {
    hasRazorpay,
    availablePaymentMethodCodes: availablePaymentMethods,
    isFetching: isFetchingAvailablePaymentMethods,
  } = useAvailablePaymentMethods();
  const { resetCart, cartBillingAddress, cartIsFreeIndiaMembership, cartIsFreeIndiaSubscriptionProduct, isCartLocked } =
    useGetShoppingCart();
  let { selectedPaymentMethodCode } = useGetSelectedPaymentMethod();
  const { createOrderAsync, isCreating } = useRazorpayCreateOrder();
  const { submitOrderAsync, isOrderSubmitting } = useRazorpaySubmitOrder();
  const triggerRazorPayErrorEvent = useRazorPayErrorEvent();
  const { clearErrors } = useClearErrors();
  const { startGlobalLoading, stopGlobalLoading } = useGlobalLoading();
  const { changePageRoute } = usePageRouting();
  const { shouldAutoSaveUserPaymentWhenRazorpay } = usePaymentAutoSaveData();
  const triggerLinkInteractionEvent = useLinkInteractionEvent();
  const { buttonText } = fields;
  const { apiKey, scriptUrl, themeColor, logoImage, upiLimitAmount } = fields.settings?.fields; // add confirmationPageUrl to data source
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAuthorizedOrderSubmitting, setIsAuthorizedOrderSubmitting] = useState(false);

  // atoms
  const resetAfterSubmit = useSetAtom(resetAfterSubmitPaymentAtom);
  const customerEmail = useAtomValue(contactEmailAtom);
  const geoIpData = useAtomValue(geoIpDataAtom);
  const isMobile = useIsMobileApp();

  const handleButtonClick = () => {
    const analyticsLinkTitleMapping = {
      [PaymentMethodCodes.RazorpayCreditCard]: 'creditcard',
      [PaymentMethodCodes.RazorpayNetbanking]: RazorpayPaymentMethods.NetBanking,
      [PaymentMethodCodes.RazorpayUpi]: RazorpayPaymentMethods.Upi,
    };

    triggerLinkInteractionEvent({
      linkTitle: analyticsLinkTitleMapping[selectedPaymentMethodCode],
      linkModule: adobeRegion,
      targetURL: '',
    });

    clearErrors();
    startGlobalLoading();
    setIsSubmitting(true);

    const request: RazorypayCreateOrderVariables = {
      paymentMethod: paymentMethodCodeMapping[selectedPaymentMethodCode],
      isRecurringPayment: shouldAutoSaveUserPaymentWhenRazorpay,
    };

    createOrderAsync(request)
      .then((response) => {
        if (response?.data?.orderId && response?.data?.customerId) {
          const sequence = getPaymentMethodSequence(availableRazorpayPaymentMethods, selectedPaymentMethodCode);
          const options: RazorpayOptions = {
            key: apiKey?.value,
            order_id: response.data.orderId,
            customer_id: response.data.customerId,
            recurring: shouldAutoSaveUserPaymentWhenRazorpay ? '1' : '0',
            notes: {
              invoice_number: response.data.orderId,
              addressId: cartBillingAddress?.addressId?.toString(),
            },
            image: logoImage?.value?.src,
            theme: {
              color: themeColor?.value,
            },
            handler: isMobile ? undefined : onSuccessPayment,
            callback_url: isMobile ? `${getCurrentPageOrigin()}/handlerazorpaydata` : undefined,
            redirect: isMobile,
            config: {
              display: {
                sequence,
                preferences: {
                  show_default_blocks: false,
                },
              },
            },
          };
          if (cartIsFreeIndiaMembership || cartIsFreeIndiaSubscriptionProduct) {
            options.notes.zeroAuth = 'true';
          }

          const razorpay = new (window as any).Razorpay(options);
          if (razorpay) {
            razorpay.on('payment.failed', (response: RazorpayAuthorizationFailureResponse) => {
              triggerRazorPayErrorEvent(response);
            });
            razorpay.open();
          }
        }
      })
      .finally(() => {
        stopGlobalLoading();
        setIsSubmitting(false);
      });
  };

  const onSuccessPayment = async (response: any) => {
    try {
      setIsAuthorizedOrderSubmitting(true);
      const { razorpay_payment_id, razorpay_order_id, razorpay_signature } = response;

      setIsSubmitting(true);

      const submitRequest: RazorpaySubmitOrderRequestVariables = {
        requestData: {
          razorpayOrderId: razorpay_order_id,
          razorpayPaymentId: razorpay_payment_id,
          razorpaySignature: razorpay_signature,
          customerEmail: customerEmail,
          verifiedCountryCode: geoIpData?.countryCode || '',
          verifiedRegionCode: geoIpData?.regionCode || '',
        },
      };

      const result: RazorpaySubmitOrderResponse = await submitOrderAsync(submitRequest);

      if (result?.data?.orderTrackingNumber) {
        const confirmUrl = `${CONFIRMATION_ROUTE}?trackingNumber=${result.data.orderTrackingNumber}`;

        resetAfterSubmit();
        resetCart();
        changePageRoute(confirmUrl);
      }
    } catch (error) {
      stopGlobalLoading();
      setIsAuthorizedOrderSubmitting(false);
      setIsSubmitting(false);
      console.error('Error processing payment response:', error);
    }
  };

  if (isFetchingAvailablePaymentMethods) {
    return null;
  }

  const availableRazorpayPaymentMethods = getAvailablePaymentRazorpayMethods(availablePaymentMethods);

  if (!hasRazorpay || !availableRazorpayPaymentMethods.some((code) => code === selectedPaymentMethodCode)) {
    return null;
  }

  if (!scriptUrl?.value) {
    console.error('Razorpay script URL is not defined');
    return null;
  }

  const isButtonDisabled = isSubmitting || isCreating || isOrderSubmitting || isCartLocked;

  if (isAuthorizedOrderSubmitting) {
    return (
      <div className="dsm page-loader-overlay">
        <PageLoader
          title={fields?.overlayTitle?.value || 'Order Processing'}
          body={fields?.overlayBody?.value || 'Please do not close this page'}
        />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <script src={scriptUrl?.value}></script>
      </Helmet>
      <Button
        className="btn btn-md btn-primary w-100"
        titleText={buttonText?.value || 'Continue to Payment'}
        onClick={handleButtonClick}
        disabled={isButtonDisabled}
      />
    </>
  );
};

export default Component;
