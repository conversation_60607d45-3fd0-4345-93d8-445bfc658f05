// tslint:disable:indent array-type
// tslint:disable:no-use-before-declare
// tslint:disable:no-namespace
// @ts-ignore
  import * as ReactJssModule from 'Foundation/ReactJss/client';
// @ts-ignore

  // The Global Payment Razorpay Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Global Payment Razorpay Settings
  // ID: 96181deb-a1cc-4faf-b956-f540291c358b
  export interface GlobalPaymentRazorpaySettingsDataSource extends ReactJssModule.BaseDataSourceItem {
  }
  // The Razorpay Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Razorpay Settings
  // ID: 48425787-f13a-49f5-9cf0-a803948e10be
  export interface RazorpaySettingsDataSource extends ReactJssModule.BaseDataSourceItem {
    // The ApiKey field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a734acd0-cb16-4e31-beed-8eca5b82c469
    // Custom Data:
    apiKey: ReactJssModule.TextField;
    // The IsCartLockFeatureEnabledForOrderFailures field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 45aaef47-5d5e-4373-b713-1570c3d50260
    // Custom Data:
    isCartLockFeatureEnabledForOrderFailures: ReactJssModule.Field<boolean>;
    // The IsPreAuthRequired field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 75ed51b2-0e5a-40c8-9493-d70d9e19450b
    // Custom Data:
    isPreAuthRequired: ReactJssModule.Field<boolean>;
    // The LogoImage field.
    // Short description:
    // Field Type: Image
    // Field ID: f3f7f54e-f902-47a7-9d6f-3244ae63f95b
    // Custom Data:
    logoImage: ReactJssModule.ImageField;
    // The MinAuthAmtRecurringPayments field.
    // Short description: in Indian Rupee (INR - ₹)
    // Field Type: Single-Line Text
    // Field ID: ce799637-b0a5-4e78-87ca-5629a66b6999
    // Custom Data:
    minAuthAmtRecurringPayments: ReactJssModule.TextField;
    // The ScriptUrl field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a089827f-cdee-470f-a781-3a5230716fbe
    // Custom Data:
    scriptUrl: ReactJssModule.TextField;
    // The ThemeColor field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0453a2ee-ec0e-4c42-ac2a-8ab2b8213ffd
    // Custom Data:
    themeColor: ReactJssModule.TextField;
    // The UPILimitAmount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7bcf436f-f393-403b-913d-83505ed2adec
    // Custom Data:
    upiLimitAmount: ReactJssModule.TextField;
  }

  // The Global Payment Razorpay Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Global Payment Razorpay Settings
  // ID: 96181deb-a1cc-4faf-b956-f540291c358b
  export interface GlobalPaymentRazorpaySettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
  }
  // The Razorpay Settings template.
  // Short description:
  // Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Razorpay Settings
  // ID: 48425787-f13a-49f5-9cf0-a803948e10be
  export interface RazorpaySettingsRenderingParams extends ReactJssModule.BaseRenderingParam {
    // The ApiKey field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a734acd0-cb16-4e31-beed-8eca5b82c469
    // Custom Data:
    apiKey: string;
    // The IsCartLockFeatureEnabledForOrderFailures field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 45aaef47-5d5e-4373-b713-1570c3d50260
    // Custom Data:
    isCartLockFeatureEnabledForOrderFailures: boolean;
    // The IsPreAuthRequired field.
    // Short description:
    // Field Type: Checkbox
    // Field ID: 75ed51b2-0e5a-40c8-9493-d70d9e19450b
    // Custom Data:
    isPreAuthRequired: boolean;
    // The LogoImage field.
    // Short description:
    // Field Type: Image
    // Field ID: f3f7f54e-f902-47a7-9d6f-3244ae63f95b
    // Custom Data:
    logoImage: string;
    // The MinAuthAmtRecurringPayments field.
    // Short description: in Indian Rupee (INR - ₹)
    // Field Type: Single-Line Text
    // Field ID: ce799637-b0a5-4e78-87ca-5629a66b6999
    // Custom Data:
    minAuthAmtRecurringPayments: string;
    // The ScriptUrl field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: a089827f-cdee-470f-a781-3a5230716fbe
    // Custom Data:
    scriptUrl: string;
    // The ThemeColor field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 0453a2ee-ec0e-4c42-ac2a-8ab2b8213ffd
    // Custom Data:
    themeColor: string;
    // The UPILimitAmount field.
    // Short description:
    // Field Type: Single-Line Text
    // Field ID: 7bcf436f-f393-403b-913d-83505ed2adec
    // Custom Data:
    upiLimitAmount: string;
  }
  export namespace GlobalPaymentRazorpaySettingsTemplate {
    export const templateId: string = '96181deb-a1cc-4faf-b956-f540291c358b';
    export const templateName: string = 'Global Payment Razorpay Settings';
  }
  export namespace RazorpaySettingsTemplate {
    export const templateId: string = '48425787-f13a-49f5-9cf0-a803948e10be';
    export const templateName: string = 'Razorpay Settings';
    export const isCartLockFeatureEnabledForOrderFailuresFieldId: string = '45aaef47-5d5e-4373-b713-1570c3d50260';
    export const isCartLockFeatureEnabledForOrderFailuresFieldName: string = 'IsCartLockFeatureEnabledForOrderFailures';
    export const isPreAuthRequiredFieldId: string = '75ed51b2-0e5a-40c8-9493-d70d9e19450b';
    export const isPreAuthRequiredFieldName: string = 'IsPreAuthRequired';
    export const apiKeyFieldId: string = 'a734acd0-cb16-4e31-beed-8eca5b82c469';
    export const apiKeyFieldName: string = 'ApiKey';
    export const logoImageFieldId: string = 'f3f7f54e-f902-47a7-9d6f-3244ae63f95b';
    export const logoImageFieldName: string = 'LogoImage';
    export const minAuthAmtRecurringPaymentsFieldId: string = 'ce799637-b0a5-4e78-87ca-5629a66b6999';
    export const minAuthAmtRecurringPaymentsFieldName: string = 'MinAuthAmtRecurringPayments';
    export const scriptUrlFieldId: string = 'a089827f-cdee-470f-a781-3a5230716fbe';
    export const scriptUrlFieldName: string = 'ScriptUrl';
    export const themeColorFieldId: string = '0453a2ee-ec0e-4c42-ac2a-8ab2b8213ffd';
    export const themeColorFieldName: string = 'ThemeColor';
    export const upiLimitAmountFieldId: string = '7bcf436f-f393-403b-913d-83505ed2adec';
    export const upiLimitAmountFieldName: string = 'UPILimitAmount';
  }
