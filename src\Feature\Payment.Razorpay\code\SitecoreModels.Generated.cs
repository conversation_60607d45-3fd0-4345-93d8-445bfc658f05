
#pragma warning disable 1591
#pragma warning disable 0108
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by Team Development for Sitecore.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;   
using System.Collections.Generic;   
using System.Linq;
using System.Text;
using Glass.Mapper.Sc.Configuration.Attributes;
using Glass.Mapper.Sc.Configuration;
using Glass.Mapper.Sc.Fields;
using Sitecore.Globalization;
using Sitecore.Data;
using Pmi.Spx.Foundation.GlassMapper.Models;



namespace Pmi.Spx.Feature.Payment.Razorpay.TemplateModels
{
 	/// <summary>
	/// IRazorpaySettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Razorpay Settings</para>	
	/// <para>ID: 48425787-f13a-49f5-9cf0-a803948e10be</para>	
	/// </summary>
	[SitecoreType(TemplateId=RazorpaySettings.TemplateId )] //, Cachable = true
	public partial interface IRazorpaySettings : IGlassBase 
	{		/// <summary>
		/// The IsCartLockFeatureEnabledForOrderFailures field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 45aaef47-5d5e-4373-b713-1570c3d50260</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.IsCartLockFeatureEnabledForOrderFailuresFieldName)]
		bool IsCartLockFeatureEnabledForOrderFailures  {get; set;} 
			/// <summary>
		/// The IsPreAuthRequired field.
		/// <para></para>
		/// <para>Field Type: Checkbox</para>		
		/// <para>Field ID: 75ed51b2-0e5a-40c8-9493-d70d9e19450b</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.IsPreAuthRequiredFieldName)]
		bool IsPreAuthRequired  {get; set;} 
			/// <summary>
		/// The ApiKey field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: a734acd0-cb16-4e31-beed-8eca5b82c469</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.ApiKeyFieldName)]
		string ApiKey  {get; set;} 
			/// <summary>
		/// The LogoImage field.
		/// <para></para>
		/// <para>Field Type: Image</para>		
		/// <para>Field ID: f3f7f54e-f902-47a7-9d6f-3244ae63f95b</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.LogoImageFieldName)]
		Image LogoImage  {get; set;} 
			/// <summary>
		/// The MinAuthAmtRecurringPayments field.
		/// <para>in Indian Rupee (INR - ₹)</para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: ce799637-b0a5-4e78-87ca-5629a66b6999</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.MinAuthAmtRecurringPaymentsFieldName)]
		string MinAuthAmtRecurringPayments  {get; set;} 
			/// <summary>
		/// The ScriptUrl field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: a089827f-cdee-470f-a781-3a5230716fbe</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.ScriptUrlFieldName)]
		string ScriptUrl  {get; set;} 
			/// <summary>
		/// The ThemeColor field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: 0453a2ee-ec0e-4c42-ac2a-8ab2b8213ffd</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.ThemeColorFieldName)]
		string ThemeColor  {get; set;} 
			/// <summary>
		/// The UPILimitAmount field.
		/// <para></para>
		/// <para>Field Type: Single-Line Text</para>		
		/// <para>Field ID: 7bcf436f-f393-403b-913d-83505ed2adec</para>
		/// <para>Custom Data: </para>
		/// </summary>
		[SitecoreField(RazorpaySettings.UPILimitAmountFieldName)]
		string UPILimitAmount  {get; set;} 
		}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class RazorpaySettings
	{
		public const string TemplateId = "48425787-f13a-49f5-9cf0-a803948e10be";
		public const string TemplateName = "Razorpay Settings";
				public const string IsCartLockFeatureEnabledForOrderFailuresFieldId = "45aaef47-5d5e-4373-b713-1570c3d50260";
		public const string IsCartLockFeatureEnabledForOrderFailuresFieldName = "IsCartLockFeatureEnabledForOrderFailures";			
				public const string IsPreAuthRequiredFieldId = "75ed51b2-0e5a-40c8-9493-d70d9e19450b";
		public const string IsPreAuthRequiredFieldName = "IsPreAuthRequired";			
				public const string ApiKeyFieldId = "a734acd0-cb16-4e31-beed-8eca5b82c469";
		public const string ApiKeyFieldName = "ApiKey";			
				public const string LogoImageFieldId = "f3f7f54e-f902-47a7-9d6f-3244ae63f95b";
		public const string LogoImageFieldName = "LogoImage";			
				public const string MinAuthAmtRecurringPaymentsFieldId = "ce799637-b0a5-4e78-87ca-5629a66b6999";
		public const string MinAuthAmtRecurringPaymentsFieldName = "MinAuthAmtRecurringPayments";			
				public const string ScriptUrlFieldId = "a089827f-cdee-470f-a781-3a5230716fbe";
		public const string ScriptUrlFieldName = "ScriptUrl";			
				public const string ThemeColorFieldId = "0453a2ee-ec0e-4c42-ac2a-8ab2b8213ffd";
		public const string ThemeColorFieldName = "ThemeColor";			
				public const string UPILimitAmountFieldId = "7bcf436f-f393-403b-913d-83505ed2adec";
		public const string UPILimitAmountFieldName = "UPILimitAmount";			
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IRazorpaySettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Razorpay Settings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Razorpay Settings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 48425787-f13a-49f5-9cf0-a803948e10be")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class RazorpaySettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public RazorpaySettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IRazorpaySettings interface instead");
		}
				[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The IsCartLockFeatureEnabledForOrderFailures field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 45aaef47-5d5e-4373-b713-1570c3d50260")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox IsCartLockFeatureEnabledForOrderFailures  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The IsPreAuthRequired field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Checkbox")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 75ed51b2-0e5a-40c8-9493-d70d9e19450b")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreCheckbox IsPreAuthRequired  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ApiKey field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: a734acd0-cb16-4e31-beed-8eca5b82c469")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ApiKey  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The LogoImage field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Image")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: f3f7f54e-f902-47a7-9d6f-3244ae63f95b")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreImage LogoImage  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The MinAuthAmtRecurringPayments field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: in Indian Rupee (INR - ₹)")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: ce799637-b0a5-4e78-87ca-5629a66b6999")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText MinAuthAmtRecurringPayments  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ScriptUrl field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: a089827f-cdee-470f-a781-3a5230716fbe")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ScriptUrl  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The ThemeColor field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 0453a2ee-ec0e-4c42-ac2a-8ab2b8213ffd")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText ThemeColor  {get; set;}		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The UPILimitAmount field.")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field Type: Single-Line Text")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Field ID: 7bcf436f-f393-403b-913d-83505ed2adec")]
		[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Custom Data: ")]
		public SPXBuildActions.TypeLiteCodeGen.MetadataTypes.SitecoreSingleLineText UPILimitAmount  {get; set;}	}
	#endif
}
namespace Pmi.Spx.Feature.Payment.Razorpay.TemplateModels
{
 	/// <summary>
	/// IGlobalPaymentRazorpaySettings Interface
	/// <para></para>
	/// <para>Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Global Payment Razorpay Settings</para>	
	/// <para>ID: 96181deb-a1cc-4faf-b956-f540291c358b</para>	
	/// </summary>
	[SitecoreType(TemplateId=GlobalPaymentRazorpaySettings.TemplateId )] //, Cachable = true
	public partial interface IGlobalPaymentRazorpaySettings : IGlassBase 
	{	}

	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ConstantMarker()]
	public static partial class GlobalPaymentRazorpaySettings
	{
		public const string TemplateId = "96181deb-a1cc-4faf-b956-f540291c358b";
		public const string TemplateName = "Global Payment Razorpay Settings";
			
	}
	#if DEBUG
	/// <summary>
	/// This class is for ts class generators. Please use the IGlobalPaymentRazorpaySettings interface instead
	/// </summary>
	[System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverage]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("The Global Payment Razorpay Settings template.")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Short description: ")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("Path: /sitecore/templates/SPX/Feature/Payment Razorpay/Global Payment Razorpay Settings")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.Comment("ID: 96181deb-a1cc-4faf-b956-f540291c358b")]
	[SPXBuildActions.TypeLiteCodeGen.MetadataTypes.ClassMarker()]
	public sealed class GlobalPaymentRazorpaySettings_Class : SPXBuildActions.TypeLiteCodeGen.MetadataTypes.TypeGenClass
	{
		public GlobalPaymentRazorpaySettings_Class()
		{
			throw new InvalidOperationException("This class is for ts class generators. Please use the IGlobalPaymentRazorpaySettings interface instead");
		}
			}
	#endif
}
