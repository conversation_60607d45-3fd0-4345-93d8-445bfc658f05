<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>14a26c8f-54f7-4736-bc8d-321b71988e6a</ProjectGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <EnableUpdateable>true</EnableUpdateable>
    <UseMerge>true</UseMerge>
    <SingleAssemblyName>Pmi.Spx.Feature.Payment.Razorpay.Tds.Master</SingleAssemblyName>
    <UseWebConfigReplacement>false</UseWebConfigReplacement>
    <DeleteAppCodeCompiledFiles>true</DeleteAppCodeCompiledFiles>
    <LegacyFileReplacement>false</LegacyFileReplacement>
    <CompactSitecoreItemsInProjectFile>True</CompactSitecoreItemsInProjectFile>
    <AssemblyName>Pmi.Spx.Feature.Payment.Razorpay.Tds.Master</AssemblyName>
    <Name>Pmi.Spx.Feature.Payment.Razorpay.Tds.Master</Name>
    <RootNamespace>Pmi.Spx.Feature.Payment.Razorpay.Tds.Master</RootNamespace>
    <ManageRoles>False</ManageRoles>
    <SitecoreDatabase>master</SitecoreDatabase>
    <AssemblyStatus>Exclude</AssemblyStatus>
    <EnableAssemblyValidation>True</EnableAssemblyValidation>
    <EnablePackageValidation>False</EnablePackageValidation>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <CodeGenTargetProject>Pmi.Spx.Feature.Payment.Razorpay</CodeGenTargetProject>
    <FieldsForCodeGen>Title,Blob,Shared,Unversioned,Default value,Validation,ValidationText,__Long description,__Short description,__Display name,__Hidden,__Read Only,__Sortorder</FieldsForCodeGen>
    <BaseTransformFile>GlassV5Item.tt</BaseTransformFile>
    <HeaderTransformFile>GlassV5Header.tt</HeaderTransformFile>
    <CodeGenFile>SitecoreModels.Generated.cs</CodeGenFile>
    <BaseNamespace>TemplateModels</BaseNamespace>
    <EnableCodeGeneration>True</EnableCodeGeneration>
    <SourceWebVirtualPath>/Pmi.Spx.Feature.Payment.Razorpay.csproj</SourceWebVirtualPath>
    <SourceWebProject>{03935a52-a9ff-40a3-8054-3a090d8d2dae}|Feature\Payment.Razorpay\code\Pmi.Spx.Feature.Payment.Razorpay.csproj</SourceWebProject>
    <SourceWebPhysicalPath>..\..\code</SourceWebPhysicalPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>.\bin\Debug\</OutputPath>
    <RecursiveDeployAction>SitecoreRecycle</RecursiveDeployAction>
    <InstallSitecoreConnector>True</InstallSitecoreConnector>
    <DisableFileDeployment>False</DisableFileDeployment>
    <LightningDeployMode>True</LightningDeployMode>
    <ConnectorTimeoutSeconds>120</ConnectorTimeoutSeconds>
    <EnableValidations>False</EnableValidations>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>false</DebugSymbols>
    <OutputPath>.\bin\Release\</OutputPath>
    <RecursiveDeployAction>Ignore</RecursiveDeployAction>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttributes Include="AssemblyFileVersion">
      <Value>$(AssemblyFileVersion)</Value>
    </AssemblyAttributes>
    <AssemblyAttributes Include="AssemblyVersion">
      <Value>$(AssemblyVersion)</Value>
    </AssemblyAttributes>
  </ItemGroup>
  <ItemGroup>
    <CodeGenTemplate Include="..\..\..\..\t4templates\glass\GeneralExtensions.tt"><Link>Code Generation Templates\GeneralExtensions.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\GlassV5Header.tt"><Link>Code Generation Templates\GlassV5Header.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\GlassV5Item.tt"><Link>Code Generation Templates\GlassV5Item.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\Helpers.tt"><Link>Code Generation Templates\Helpers.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\Inflector.tt"><Link>Code Generation Templates\Inflector.tt</Link>
    </CodeGenTemplate><CodeGenTemplate Include="..\..\..\..\t4templates\glass\StringExtensions.tt"><Link>Code Generation Templates\StringExtensions.tt</Link>
    </CodeGenTemplate></ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\code\Pmi.Spx.Feature.Payment.Razorpay.csproj">
      <Project>{1a40e4df-6a04-4227-aaba-0273c9674797}</Project>
      <Name>Pmi.Spx.Feature.Payment.Razorpay</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <SitecoreItem Include="sitecore\content.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization><SitecoreName>content</SitecoreName></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Page Content Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Page Content Settings\Errors.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Page Content Settings\Errors\Razorpay Payment Handler Failed.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Payment Razorpay Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\content\SPX\Global Settings\Global Payment Razorpay Settings\Razorpay Settings.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\layout.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Payment Razorpay.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Payment Razorpay\Hosted.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Payment Razorpay\Netbanking.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Payment Razorpay\PaymentFailed.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\system\Dictionary\SPX\Feature\Payment Razorpay\Upi.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates.item"><ItemDeployment>NeverDeploy</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature.item"><ItemDeployment>DeployOnce</ItemDeployment><ChildItemSynchronization>NoChildSynchronization</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Global Payment Razorpay Settings.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Global Payment Razorpay Settings\__Standard Values.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\__Standard Values.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\FeatureSwitchSettings.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\FeatureSwitchSettings\IsCartLockFeatureEnabledForOrderFailures.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\FeatureSwitchSettings\IsPreAuthRequired.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\Settings.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\Settings\ApiKey.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\Settings\LogoImage.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\Settings\MinAuthAmtRecurringPayments.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\Settings\ScriptUrl.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\Settings\ThemeColor.item"><ItemDeployment>AlwaysUpdate</ItemDeployment><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization></SitecoreItem>
    <SitecoreItem Include="sitecore\templates\SPX\Feature\Payment Razorpay\Razorpay Settings\Settings\UPILimitAmount.item"><ChildItemSynchronization>KeepAllChildrenSynchronized</ChildItemSynchronization><ItemDeployment>AlwaysUpdate</ItemDeployment></SitecoreItem>
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <Import Project="..\..\..\..\packages\HedgehogDevelopment.TDS.********\build\HedgehogDevelopment.TDS.targets" Condition="Exists('..\..\..\..\packages\HedgehogDevelopment.TDS.********\build\HedgehogDevelopment.TDS.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\..\packages\HedgehogDevelopment.TDS.********\build\HedgehogDevelopment.TDS.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\..\packages\HedgehogDevelopment.TDS.********\build\HedgehogDevelopment.TDS.targets'))" />
  </Target>
</Project>